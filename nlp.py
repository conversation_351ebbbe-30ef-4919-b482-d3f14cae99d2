import os
from dotenv import load_dotenv
import openai  # For accessing GPT models
import requests
import json
import re
import random
import string
import numpy as np
from collections import Counter
from textblob import TextBlob
import spacy

# Load environment variables (for API keys)
load_dotenv()

# Configure OpenAI (you'll need an API key)
openai.api_key = os.getenv("OPENAI_API_KEY")

# Load spaCy model
try:
    nlp = spacy.load('en_core_web_sm')
except:
    print("Please install spaCy model with: python -m spacy download en_core_web_sm")
    nlp = None

# Track conversation state and collect training data
conversation_context = {
    "user_name": None,
    "financial_goals": [],
    "current_topic": "greeting",
    "amounts_mentioned": [],
    "conversation_history": [],
    "collected_data": []  # Store data for future ML training
}

# Financial domain knowledge
financial_terms = {
    "income_sources": ["freelancing", "consulting", "e-commerce", "content creation", 
                      "affiliate marketing", "dropshipping", "youtube", "blogging", 
                      "social media", "online courses", "digital products", "stock photography"],
    "investment_types": ["stocks", "bonds", "etfs", "index funds", "real estate", 
                        "cryptocurrency", "mutual funds", "reit", "p2p lending", 
                        "commodities", "forex", "options", "futures"],
    "financial_concepts": ["compound interest", "diversification", "dollar cost averaging", 
                          "asset allocation", "risk tolerance", "liquidity", "inflation", 
                          "tax efficiency", "portfolio rebalancing", "passive income"]
}

def extract_features(text, doc=None):
    """Extract NLP features for potential ML use"""
    if doc is None and nlp:
        doc = nlp(text)
    
    # Basic text features
    word_count = len(text.split())
    char_count = len(text)
    sentence_count = len(list(doc.sents)) if doc else text.count('.') + text.count('!') + text.count('?')
    
    # Lexical features
    punctuation_count = sum(1 for char in text if char in string.punctuation)
    uppercase_count = sum(1 for char in text if char.isupper())
    
    # Part-of-speech features if spaCy is available
    pos_counts = {}
    if doc:
        pos_counts = Counter([token.pos_ for token in doc])
    
    # Financial term presence
    financial_term_counts = {}
    for category, terms in financial_terms.items():
        financial_term_counts[category] = sum(1 for term in terms if term in text.lower())
    
    # Question features
    question_words = ["how", "what", "when", "where", "why", "who", "which"]
    contains_question_mark = "?" in text
    contains_question_word = any(word in text.lower().split() for word in question_words)
    
    # Sentiment using TextBlob
    blob = TextBlob(text)
    sentiment = blob.sentiment.polarity
    
    return {
        "text_features": {
            "word_count": word_count,
            "char_count": char_count,
            "sentence_count": sentence_count,
            "punctuation_count": punctuation_count,
            "uppercase_count": uppercase_count
        },
        "pos_counts": pos_counts,
        "financial_term_counts": financial_term_counts,
        "question_features": {
            "contains_question_mark": contains_question_mark,
            "contains_question_word": contains_question_word
        },
        "sentiment": sentiment
    }

def extract_entities(doc):
    """Extract named entities and financial entities"""
    if not doc:
        return []
    
    # Get standard named entities
    entities = [(ent.text, ent.label_) for ent in doc.ents]
    
    # Extract money and numerical entities
    money_entities = [ent.text for ent in doc.ents if ent.label_ == "MONEY"]
    number_entities = [ent.text for ent in doc.ents if ent.label_ == "CARDINAL"]
    
    # Extract custom financial entities using pattern matching
    financial_entities = []
    for category, terms in financial_terms.items():
        for term in terms:
            if term in doc.text.lower():
                financial_entities.append((term, category))
    
    return {
        "named_entities": entities,
        "money_entities": money_entities,
        "number_entities": number_entities,
        "financial_entities": financial_entities
    }

def extract_time_references(text):
    """Extract time-related terms for timeline analysis"""
    time_terms = ["year", "month", "week", "day", "hour", "minute", "second",
                 "decade", "century", "quarter", "annual", "monthly", "weekly",
                 "daily", "soon", "later", "now", "future", "past", "present"]
    
    time_references = []
    for word in text.lower().split():
        if any(term in word for term in time_terms):
            time_references.append(word)
    
    # Extract specific time patterns
    time_patterns = [
        r'\d+\s+years?',
        r'\d+\s+months?',
        r'\d+\s+weeks?',
        r'\d+\s+days?',
        r'next\s+\w+',
        r'last\s+\w+',
        r'in\s+\d+\s+\w+'
    ]
    
    for pattern in time_patterns:
        matches = re.findall(pattern, text.lower())
        time_references.extend(matches)
    
    return time_references

def extract_amounts(text):
    """Extract monetary amounts from text"""
    # Match patterns like $100, $1,000, $1.5 million
    amount_patterns = [
        r'\$\d+(?:,\d+)*(?:\.\d+)?',
        r'\d+(?:,\d+)*(?:\.\d+)?\s+dollars',
        r'\$\d+(?:,\d+)*(?:\.\d+)?\s+(?:thousand|million|billion)',
        r'\d+(?:,\d+)*(?:\.\d+)?\s+(?:thousand|million|billion)\s+dollars'
    ]
    
    amounts = []
    for pattern in amount_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        amounts.extend(matches)
    
    return amounts

def extract_keywords(text, doc=None):
    """Extract important keywords from text"""
    if doc is None and nlp:
        doc = nlp(text)
    
    # Extract keywords using spaCy if available
    if doc:
        # Get nouns and proper nouns
        keywords = [token.text for token in doc if token.pos_ in ["NOUN", "PROPN"] and not token.is_stop]
        
        # Get financial terms
        for category, terms in financial_terms.items():
            for term in terms:
                if term in text.lower() and term not in keywords:
                    keywords.append(term)
    else:
        # Fallback to simple word extraction
        words = text.lower().split()
        stopwords = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "with", "by"]
        keywords = [word for word in words if word not in stopwords and len(word) > 2]
    
    return keywords

def determine_intent(text, doc=None):
    """Determine user intent with sophisticated pattern matching"""
    if doc is None and nlp:
        doc = nlp(text)
    
    text = text.lower()
    
    # Define intent patterns with weighted scores
    intent_patterns = {
        "greeting": {
            "patterns": ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening"],
            "weight": 1.0
        },
        "introduction": {
            "patterns": ["my name is", "i am", "call me", "i'm", "name's"],
            "weight": 1.0
        },
        "income_inquiry": {
            "patterns": ["make money", "earn money", "generate income", "income source", "ways to earn"],
            "weight": 1.2
        },
        "investment_advice": {
            "patterns": ["invest", "investing", "investment", "stock", "bond", "etf", "fund", "portfolio"],
            "weight": 1.2
        },
        "goal_setting": {
            "patterns": ["goal", "target", "aim", "plan", "achieve", "want to", "hope to"],
            "weight": 1.1
        },
        "timeline_question": {
            "patterns": ["how long", "when", "how soon", "timeline", "time frame", "years", "months"],
            "weight": 1.0
        },
        "risk_assessment": {
            "patterns": ["risk", "safe", "secure", "volatile", "stability", "dangerous", "conservative"],
            "weight": 1.1
        },
        "budget_planning": {
            "patterns": ["budget", "spending", "expense", "cost", "afford", "save", "saving"],
            "weight": 1.0
        },
        "farewell": {
            "patterns": ["bye", "goodbye", "see you", "talk later", "exit", "quit", "end"],
            "weight": 0.9
        }
    }
    
    # Calculate scores for each intent
    intent_scores = {}
    for intent, data in intent_patterns.items():
        score = 0
        for pattern in data["patterns"]:
            if pattern in text:
                score += data["weight"]
        intent_scores[intent] = score
    
    # Add contextual scoring
    if "?" in text:
        for intent in ["income_inquiry", "investment_advice", "timeline_question", "risk_assessment"]:
            intent_scores[intent] *= 1.2
    
    # Get the highest scoring intent
    max_intent = max(intent_scores.items(), key=lambda x: x[1])
    
    # If no clear intent is found, return general
    if max_intent[1] == 0:
        return "general"
    
    return max_intent[0]

def analyze_text(user_input):
    """Comprehensive analysis of user input for ML preparation"""
    # Process with spaCy if available
    doc = nlp(user_input) if nlp else None
    
    # Extract features for potential ML use
    features = extract_features(user_input, doc)
    
    # Extract entities
    entities = extract_entities(doc) if doc else []
    
    # Extract time references
    time_references = extract_time_references(user_input)
    
    # Extract monetary amounts
    amounts = extract_amounts(user_input)
    
    # Extract keywords
    keywords = extract_keywords(user_input, doc)
    
    # Determine intent
    intent = determine_intent(user_input, doc)
    
    # TextBlob for sentiment
    blob = TextBlob(user_input)
    
    # Prepare analysis result
    analysis = {
        "text": user_input,
        "features": features,
        "entities": entities,
        "time_references": time_references,
        "amounts": amounts,
        "keywords": keywords,
        "intent": intent,
        "is_question": "?" in user_input,
        "sentiment": blob.sentiment.polarity,
        "blob": blob
    }
    
    # Store analysis for future ML training
    conversation_context["collected_data"].append(analysis)
    
    return analysis

def save_training_data(filename="financial_nlp_data.json"):
    """Save collected data for future ML training"""
    with open(filename, 'w') as f:
        json.dump(conversation_context["collected_data"], f, default=str)
    print(f"Saved {len(conversation_context['collected_data'])} training examples to {filename}")

def use_gpt_for_complex_queries(user_input, context):
    """Use GPT for complex financial questions we can't handle with rules"""
    # Only use this for complex queries to save on API costs
    
    # Prepare conversation history for context
    messages = [
        {"role": "system", "content": "You are Money Man, a financial assistant focused on helping users generate online income, invest wisely, and achieve financial goals. Keep responses concise and practical."}
    ]
    
    # Add recent conversation history for context
    for exchange in context["conversation_history"][-5:]:  # Last 5 exchanges
        messages.append({"role": "user", "content": exchange["user"]})
        messages.append({"role": "assistant", "content": exchange["assistant"]})
    
    # Add current query
    messages.append({"role": "user", "content": user_input})
    
    try:
        # Use GPT-3.5-turbo to save costs (or GPT-4 for more complex queries)
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",  # Much cheaper than GPT-4
            messages=messages,
            max_tokens=150,  # Keep responses short to reduce costs
            temperature=0.7
        )
        return response.choices[0].message["content"]
    except Exception as e:
        print(f"Error using GPT API: {e}")
        return None  # Fall back to rule-based responses

def should_use_gpt(analysis, user_input):
    """Determine if we should use GPT for this query"""
    # Use GPT for complex queries, specific financial advice, or when our rules aren't matching
    
    # If it's a simple greeting or name exchange, use rules
    if len(user_input.split()) <= 5:
        return False
        
    # If we detect specific intents we can handle, use rules
    if analysis["intent"] in ["greeting", "introduction", "simple_question"]:
        return False
        
    # If it contains complex financial terms or specific questions, use GPT
    complex_terms = ["diversification", "portfolio", "allocation", "dividend", "yield", 
                    "compound interest", "tax", "inflation", "recession"]
    
    if any(term in user_input.lower() for term in complex_terms):
        return True
        
    # If it's a longer, more complex query
    if len(user_input.split()) > 15 and analysis["is_question"]:
        return True
        
    return False  # Default to rule-based

def generate_response(user_input, analysis, context):
    """Generate response using hybrid approach"""
    # Update conversation history
    if len(context["conversation_history"]) >= 10:
        context["conversation_history"].pop(0)  # Remove oldest exchange
    
    # Decide whether to use GPT or rule-based response
    if should_use_gpt(analysis, user_input):
        response = use_gpt_for_complex_queries(user_input, context)
        if response:  # If GPT response was successful
            context["conversation_history"].append({"user": user_input, "assistant": response})
            return response
    
    # Fall back to rule-based responses
    response = generate_rule_based_response(analysis, context)
    
    # Update conversation history
    context["conversation_history"].append({"user": user_input, "assistant": response})
    
    return response

# Main conversation loop
if __name__ == "__main__":
    print("Money Man - Financial Assistant")
    print("Your AI guide to financial freedom")
    
    # Start with a greeting
    print("\nMoney Man: Hi there! I'm Money Man, your financial assistant. What's your name?")
    conversation_context["current_topic"] = "introduction"
    
    while True:
        user_input = input("\nYou: ")
        if user_input.lower() in ["exit", "quit", "bye"]:
            print("\nMoney Man: Thanks for chatting! Good luck with your financial journey.")
            break
            
        # Analyze the input
        analysis = analyze_text(user_input)
        
        # Generate a response based on analysis and context
        response = generate_response(user_input, analysis, conversation_context)
        
        print(f"\nMoney Man: {response}")
